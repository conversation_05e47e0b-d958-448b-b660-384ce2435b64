<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="HtmlUnknownAttribute" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="myValues">
        <value>
          <list size="1">
            <item index="0" class="java.lang.String" itemvalue="?" />
          </list>
        </value>
      </option>
      <option name="myCustomValuesEnabled" value="true" />
    </inspection_tool>
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="10">
            <item index="0" class="java.lang.String" itemvalue="weaviate-client" />
            <item index="1" class="java.lang.String" itemvalue="httpx" />
            <item index="2" class="java.lang.String" itemvalue="openai" />
            <item index="3" class="java.lang.String" itemvalue="fastapi" />
            <item index="4" class="java.lang.String" itemvalue="langchain" />
            <item index="5" class="java.lang.String" itemvalue="sentence-transformers" />
            <item index="6" class="java.lang.String" itemvalue="aiohttp" />
            <item index="7" class="java.lang.String" itemvalue="asyncmy" />
            <item index="8" class="java.lang.String" itemvalue="elasticsearch" />
            <item index="9" class="java.lang.String" itemvalue="redis" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>